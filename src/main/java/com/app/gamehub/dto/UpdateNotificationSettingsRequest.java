package com.app.gamehub.dto;

import com.app.gamehub.enums.ActivityType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(description = "更新通知设置请求")
public class UpdateNotificationSettingsRequest {

  @NotNull(message = "账号ID不能为空")
  @Schema(description = "游戏账号ID", example = "1")
  private Long accountId;

  @NotNull(message = "接收通知设置不能为空")
  @Schema(description = "是否接收联盟通知", example = "true")
  private Boolean receiveAllianceNotifications;

  @Schema(description = "接收的通知类型列表")
  private List<ActivityType> notificationTypes;
}
