package com.app.gamehub.enums;

import lombok.Getter;

@Getter
public enum ActivityType {
  SAN_YING_ZHAN_LV_BU("三英战吕布"),
  GUAN_DU_BAO_MING("官渡报名"),
  ZHU_JIU_LUN_YING_XIONG("煮酒论英雄"),
  NAN_MAN_RU_QIN("南蛮入侵"),
  GONG_CHENG("攻城"),
  SHOU_CHENG("守城"),
  SHUA_GONG_XUN("刷功勋");

  private final String displayName;

  ActivityType(String displayName) {
    this.displayName = displayName;
  }

  public static ActivityType fromDisplayName(String displayName) {
    for (ActivityType type : values()) {
      if (type.displayName.equals(displayName)) {
        return type;
      }
    }
    throw new IllegalArgumentException("未知的活动类型: " + displayName);
  }
}
