package com.app.gamehub.service;

import com.app.gamehub.dto.SendAllianceNotificationRequest;
import com.app.gamehub.dto.UpdateNotificationSettingsRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.entity.User;
import com.app.gamehub.enums.ActivityType;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.UserRepository;
import com.app.gamehub.util.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AllianceNotificationService {

  private final AllianceRepository allianceRepository;
  private final GameAccountRepository gameAccountRepository;
  private final UserRepository userRepository;
  private final WeChatService weChatService;
  private final MessageSubscriptionService messageSubscriptionService;

  private static final String TEMPLATE_ID = "i4cXxSfmC7eSUaZxRL4u8sT_eBPCQ5RV_xmW0tmV85c";
  private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

  /**
   * 发送联盟活动通知
   */
  @Transactional
  public void sendAllianceNotification(SendAllianceNotificationRequest request) {
    Long currentUserId = UserContext.getUserId();
    
    // 验证联盟存在且当前用户是盟主
    Alliance alliance = allianceRepository.findById(request.getAllianceId())
        .orElseThrow(() -> new BusinessException("联盟不存在"));
    
    if (!alliance.getLeaderId().equals(currentUserId)) {
      throw new BusinessException("只有盟主可以发送联盟通知");
    }

    // 获取联盟所有成员账号
    List<GameAccount> members = gameAccountRepository.findByAllianceId(request.getAllianceId());
    
    String activityName = request.getActivityType().getDisplayName();
    String startTimeStr = request.getStartTime().format(DATE_FORMATTER);
    String sender = String.format("%s（%d区）", alliance.getName(), alliance.getServerId());
    
    int successCount = 0;
    int failCount = 0;

    for (GameAccount account : members) {
      try {
        // 检查账号是否接收通知
        if (!shouldReceiveNotification(account, request.getActivityType())) {
          log.debug("账号 {} 未开启 {} 类型通知，跳过发送", account.getId(), activityName);
          continue;
        }

        // 检查用户是否有足够的订阅数量
        if (!messageSubscriptionService.hasEnoughSubscription(account.getUserId(), 1)) {
          log.warn("用户 {} 消息订阅数量不足，跳过发送通知", account.getUserId());
          continue;
        }

        // 获取用户当前剩余消息数量
        Integer remainingCount = messageSubscriptionService.getSubscriptionCount(account.getUserId());
        
        // 构建备注信息
        String remark = request.getRemark();
        if (remark == null || remark.trim().isEmpty()) {
          remark = "活动时间为活动预计开启时间，如有变更，盟主将另行通知";
        }
        remark += String.format("（预计剩余可接收%d条通知）", remainingCount - 1);

        // 发送微信通知
        sendWeChatNotification(account.getUser(), sender, activityName, startTimeStr, remark);
        
        // 减少用户订阅数量
        messageSubscriptionService.decreaseSubscriptionCount(account.getUserId(), 1);
        
        successCount++;
        log.info("成功发送联盟通知给用户 {}, 活动: {}", account.getUserId(), activityName);
        
      } catch (Exception e) {
        failCount++;
        log.error("发送联盟通知失败，用户: {}, 活动: {}, 错误: {}", 
            account.getUserId(), activityName, e.getMessage());
      }
    }

    log.info("联盟通知发送完成，成功: {}, 失败: {}", successCount, failCount);
  }

  /**
   * 更新账号通知设置
   */
  @Transactional
  public GameAccount updateNotificationSettings(UpdateNotificationSettingsRequest request) {
    Long currentUserId = UserContext.getUserId();
    
    GameAccount account = gameAccountRepository.findById(request.getAccountId())
        .orElseThrow(() -> new BusinessException("游戏账号不存在"));
    
    // 验证账号所有权
    if (!account.getUserId().equals(currentUserId)) {
      throw new BusinessException("只能修改自己的账号设置");
    }

    account.setReceiveAllianceNotifications(request.getReceiveAllianceNotifications());
    
    if (request.getNotificationTypes() != null) {
      String notificationTypesStr = request.getNotificationTypes().stream()
          .map(ActivityType::getDisplayName)
          .collect(Collectors.joining(","));
      account.setNotificationTypes(notificationTypesStr);
    }

    GameAccount savedAccount = gameAccountRepository.save(account);
    log.info("更新账号 {} 通知设置成功", request.getAccountId());
    
    return savedAccount;
  }

  /**
   * 检查账号是否应该接收指定类型的通知
   */
  private boolean shouldReceiveNotification(GameAccount account, ActivityType activityType) {
    if (!account.getReceiveAllianceNotifications()) {
      return false;
    }

    String notificationTypes = account.getNotificationTypes();
    if (notificationTypes == null || notificationTypes.trim().isEmpty()) {
      return false;
    }

    List<String> enabledTypes = Arrays.asList(notificationTypes.split(","));
    return enabledTypes.contains(activityType.getDisplayName());
  }

  /**
   * 发送微信通知
   */
  private void sendWeChatNotification(User user, String sender, String activityName, 
                                    String startTime, String remark) {
    // 这里应该调用微信小程序的消息推送API
    // 由于需要access_token和具体的微信API实现，这里先记录日志
    log.info("发送微信通知 - 用户: {}, 发起方: {}, 活动: {}, 时间: {}, 备注: {}", 
        user.getOpenid(), sender, activityName, startTime, remark);
    
    // TODO: 实现实际的微信消息推送
    // weChatService.sendTemplateMessage(user.getOpenid(), TEMPLATE_ID, templateData);
  }
}
